---
description: General overview and context
globs:
alwaysApply: true
---

# Project Description

This is the official NiceGUI repository. With NiceGUI you can write graphical user interfaces which run in the browser. It has a very gentle learning curve while still offering the option for advanced customizations. NiceGUI follows a backend-first philosophy: it handles all the web development details for the developer, so they can focus on writing Python code. This makes it ideal for a wide range of projects including short scripts, dashboards, robotics projects, IoT solutions, smart home automation, and machine learning.

# Technical Stack

- Python/FastAPI for the backend
- Vue/Quasar in the frontend
- socket.io for communication between frontend and backend

# Design Decisions

- All user interactions are send to the backend and invoke the proper Python functions.
- Unlike Streamlit the framework took much care to allow easy integration with any other Python libraries.
- NiceGUI only uses one uvicorn worker (to not have to implement/support tricky synchronization).
- The socket.io library is used for managing web sockets. After the initial content is loaded a web socket connection is established and kept open for communication as long as the web page is shown.
