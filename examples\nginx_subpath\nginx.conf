
user  nginx;
worker_processes  auto;

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;


events {
    worker_connections  1024;
}


http {

    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format upstreamlog '[$time_local] $remote_addr - $remote_user - $server_name to: $upstream_addr: $request upstream_response_time $upstream_response_time msec $msec request_time $request_time';

    access_log  /var/log/nginx/access.log upstreamlog;
    error_log /var/log/nginx/error.log;

    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    ssl_protocols TLSv1 TLSv1.1 TLSv1.2; # Dropping SSLv3, ref: POODLE
    ssl_prefer_server_ciphers on;

    client_max_body_size 200M;

    map $http_upgrade $connection_upgrade {
        default upgrade;
        '' close;
    }

    server {
        listen 80 default_server;
        server_name _;
        resolver 127.0.0.11; # see https://github.com/docker/compose/issues/3412

        # Ensure /nicegui (without trailing slash) redirects to /nicegui/
        location = /nicegui {
            return 301 /nicegui/;
        }

        # Proxy everything under /nicegui/ to the app service
        location /nicegui/ {
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection $connection_upgrade;
            proxy_set_header Authorization $http_authorization;
            proxy_pass_header Authorization;
            proxy_set_header Host $host;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

            # With a trailing slash, nginx strips the matching prefix (/nicegui/) and passes the remainder
            proxy_pass http://app:8080/;
            proxy_set_header X-Forwarded-Prefix /nicegui;
            # Rewrite absolute redirects from upstream to include the /nicegui prefix
            proxy_redirect ~^https?://[^/]+(/.*)$ /nicegui$1;
        }
    }
}
