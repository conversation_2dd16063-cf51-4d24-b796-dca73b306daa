# Descope Auth Example

Descope is an all-inclusive user authentication and user management platform.

## Getting Started

1. Create a [Descope](https://www.descope.com/) account.
2. Setup a project and configure the "Login Flow" (first step in the Getting Started Wizard).
3. Instead of following the "Integrate" instructions of the <PERSON>, use this example.
4. Provide your Descope Project ID as environment variable: `DESCOPE_PROJECT_ID=<your_project_id> python3 main.py`
