# 06: Styling & Theming

## Overview

This guide covers NiceGUI's comprehensive styling and theming system. NiceGUI provides multiple approaches to customize the visual appearance of your applications:
- **Tailwind CSS** integration with fluent API and auto-completion
- **Quasar Framework** props for component-level styling
- **CSS Variables** for global theming
- **Dark Mode** support with reactive switching
- **Custom Colors** and branding system
- **SASS/SCSS** integration for advanced styling

Use this guide when you need to customize appearance, implement responsive designs, create consistent visual themes, or integrate with design systems.

## Core Concepts

### Three-Layer Styling Architecture

NiceGUI follows a layered styling approach:

1. **Base Layer**: CSS variables and global styles
2. **Framework Layer**: Quasar component props and Vue directives
3. **Utility Layer**: Tailwind CSS classes for rapid styling

### Backend-First Philosophy

All styling is defined in Python code - no separate CSS files needed for basic customization. Advanced users can add custom CSS when required.

### Styling Methods Priority

When multiple styling methods are applied to an element:
1. Inline `style()` takes highest precedence
2. `classes()` and `tailwind()` methods follow
3. `props()` component properties
4. CSS variables and global styles

## Quick Start

### Basic Element Styling

```python
from nicegui import ui

# Using Tailwind classes - most common approach
ui.button('Click me').classes('bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded')

# Using Quasar props for component-specific styling
ui.button('Styled Button').props('color=primary outline round')

# Using inline CSS styles when needed
ui.label('Custom Label').style('color: #6E93D6; font-size: 200%; font-weight: 300')

# Combining multiple approaches
ui.input('Email').classes('w-full').props('outlined').style('margin-top: 1rem')

ui.run()
```

### Fluent Tailwind API

```python
from nicegui import ui, Tailwind

# Auto-completion friendly Tailwind styling
ui.label('Featured Text').tailwind.font_weight('extrabold').text_color('blue-600').background_color('orange-200')

# Chain multiple classes together
ui.button('Action').tailwind.padding('4').margin('2').background_color('green-500').text_color('white').rounded('lg')

# Apply predefined Tailwind styles to multiple elements
highlight_style = Tailwind().text_color('red-600').font_weight('bold')
ui.label('Important').tailwind(highlight_style)
ui.label('Critical').tailwind(highlight_style)

ui.run()
```

## Best Practices

### Styling Method Selection

**Use Tailwind classes for:**
- Layout and spacing (margins, padding, flexbox)
- Typography (fonts, sizes, weights)
- Colors and backgrounds
- Responsive design breakpoints
- Hover and focus states

**Use Quasar props for:**
- Component-specific features (button colors, input variants)
- Material Design styling options
- Component state management (loading, disabled)
- Icon placement and sizing

**Use inline CSS styles for:**
- Complex animations and transitions
- Custom properties not available in Tailwind
- Dynamic styling based on runtime values
- Legacy CSS integration

### Color and Theme Consistency

```python
from nicegui import ui

# Set global color scheme at app start
ui.colors(
    primary='#1976d2',     # Main brand color
    secondary='#26a69a',   # Accent color
    accent='#9c27b0',      # Highlight color
    positive='#21ba45',    # Success states
    negative='#c10015',    # Error states
    info='#31ccec',        # Info messages
    warning='#f2c037'      # Warning states
)

# Define custom brand colors
ui.colors(brand='#424242')
ui.button('Brand Action', color='brand')
ui.label('Brand Text').classes('text-brand')
```

### Responsive Design Patterns

```python
from nicegui import ui

# Mobile-first responsive layout
with ui.row().classes('flex-col sm:flex-row w-full gap-4'):
    with ui.column().classes('w-full sm:w-1/2 lg:w-1/3'):
        ui.label('Sidebar Content')
    with ui.column().classes('w-full sm:w-1/2 lg:w-2/3'):
        ui.label('Main Content')

# Responsive typography
ui.label('Heading').classes('text-2xl sm:text-3xl lg:text-4xl font-bold')

# Hide/show elements at different breakpoints
ui.button('Desktop Action').classes('hidden sm:inline-block')
ui.button('Mobile Action').classes('sm:hidden')
```

## Common Patterns

### Dark Mode Implementation

```python
from nicegui import ui

# Global dark mode toggle
dark_mode = ui.dark_mode()

with ui.header().classes('items-center justify-between'):
    ui.label('My App')
    ui.switch('Dark Mode').bind_value(dark_mode)

# Dark mode responsive styling
ui.card().classes('bg-white dark:bg-gray-800 text-black dark:text-white')

ui.run()
```

### Custom Component Styling

```python
from nicegui import ui
from contextlib import contextmanager

@contextmanager
def styled_card(title: str):
    """Reusable styled card component"""
    with ui.card().classes('w-full max-w-md shadow-lg'):
        ui.label(title).classes('text-xl font-bold mb-4')
        with ui.column().classes('w-full gap-2'):
            yield

# Usage
with styled_card('User Profile'):
    ui.input('Name').props('outlined')
    ui.input('Email').props('outlined')
    ui.button('Save').classes('self-end')
```

### Dynamic Theme Switching

```python
from nicegui import ui
from typing import Dict

# Theme configurations
THEMES: Dict[str, Dict[str, str]] = {
    'light': {
        'primary': '#1976d2',
        'background': 'bg-white',
        'text': 'text-gray-900'
    },
    'dark': {
        'primary': '#90caf9',
        'background': 'bg-gray-900',
        'text': 'text-gray-100'
    },
    'brand': {
        'primary': '#6E93D6',
        'background': 'bg-blue-50',
        'text': 'text-blue-900'
    }
}

def apply_theme(theme_name: str):
    """Apply theme configuration dynamically"""
    theme = THEMES[theme_name]
    ui.colors(primary=theme['primary'])
    # Update element classes dynamically
    container.classes(replace=f"{theme['background']} {theme['text']}")

# Theme selector
container = ui.column().classes('w-full min-h-screen p-4')
with container:
    theme_select = ui.select(
        options=list(THEMES.keys()),
        value='light',
        on_change=lambda e: apply_theme(e.value)
    )
    ui.label('Content adapts to selected theme')

ui.run()
```

### Layout with CSS Grid

```python
from nicegui import ui

# CSS Grid layout using Tailwind
ui.add_head_html('''
<style type="text/tailwindcss">
    .dashboard-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1rem;
    }
    .card-tall {
        grid-row: span 2;
    }
</style>
''')

with ui.element().classes('dashboard-grid'):
    with ui.card().classes('p-4'):
        ui.label('Widget 1')
    with ui.card().classes('p-4 card-tall'):
        ui.label('Tall Widget')
    with ui.card().classes('p-4'):
        ui.label('Widget 3')
```

## Advanced Techniques

### Custom Tailwind Layers

```python
from nicegui import ui

# Define custom component classes
ui.add_head_html('''
<style type="text/tailwindcss">
    @layer components {
        .btn-primary {
            @apply bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition-colors duration-200;
        }
        .card-elevated {
            @apply bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 border border-gray-200 dark:border-gray-700;
        }
        .input-modern {
            @apply border-2 border-gray-300 focus:border-blue-500 rounded-md px-3 py-2 outline-none transition-colors;
        }
    }
</style>
''')

# Use custom component classes
ui.button('Primary Action').classes('btn-primary')
with ui.element().classes('card-elevated'):
    ui.input().classes('input-modern')
```

### CSS Variable Theming

```python
from nicegui import ui

# Define CSS variables for theming
ui.add_css('''
    :root {
        --primary-color: #1976d2;
        --secondary-color: #26a69a;
        --background-color: #f5f5f5;
        --text-color: #333;
        --border-radius: 8px;
        --box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .themed-button {
        background-color: var(--primary-color);
        color: white;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        padding: 0.5rem 1rem;
        border: none;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .themed-button:hover {
        background-color: var(--secondary-color);
    }
''')

# Use CSS variables in components
ui.button('Themed Button').classes('themed-button')
```

### SASS Integration

```python
from nicegui import ui

# Add SASS styles (requires sass extra: pip install nicegui[sass])
ui.add_scss('''
$primary-color: #1976d2;
$secondary-color: #26a69a;
$border-radius: 8px;

@mixin button-style($bg-color) {
    background-color: $bg-color;
    border-radius: $border-radius;
    padding: 0.5rem 1rem;
    color: white;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
        background-color: darken($bg-color, 10%);
        transform: translateY(-1px);
    }
}

.sass-primary-btn {
    @include button-style($primary-color);
}

.sass-secondary-btn {
    @include button-style($secondary-color);
}
''')

ui.button('SASS Primary').classes('sass-primary-btn')
ui.button('SASS Secondary').classes('sass-secondary-btn')
```

### Animation and Transitions

```python
from nicegui import ui

# CSS animations with Tailwind
ui.add_head_html('''
<style type="text/tailwindcss">
    @layer utilities {
        .animate-fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }
        .animate-slide-up {
            animation: slideUp 0.3s ease-out;
        }
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    @keyframes slideUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
''')

# Animated elements
ui.card().classes('animate-fade-in p-4').with_(
    ui.label('Fades in on load')
)

def show_notification():
    with ui.element().classes('animate-slide-up'):
        ui.notify('Animated notification!')

ui.button('Show Animation', on_click=show_notification)
```

## Integration Points

### Vue Component Styling

NiceGUI elements inherit Quasar/Vue styling capabilities. Use `props()` method to access full Quasar component API:

```python
# Quasar button props
ui.button('Button').props('round outline color=primary size=lg icon=star')

# Quasar input props
ui.input().props('outlined dense hide-bottom-space prefix-icon=search')

# Custom Vue directives
ui.element('div').props('v-ripple')  # Quasar ripple effect
```

### Integration with FastAPI

Style FastAPI-served pages consistently:

```python
from nicegui import ui, app
from fastapi import Request

@app.get('/api-page')
def api_page(request: Request):
    # Apply consistent styling to API-generated pages
    ui.colors(primary='#1976d2')  # Match main app colors
    with ui.column().classes('container mx-auto p-4'):
        ui.label('API Generated Page')
    return ui.run(request=request)
```

### Custom Element Styling

```python
from nicegui import ui

# Style custom HTML elements
custom_element = ui.element('custom-component')
custom_element.classes('border-2 border-blue-500 rounded-lg p-4')
custom_element.style('background: linear-gradient(45deg, #f0f9ff, #e0f2fe)')

# Add custom CSS for third-party components
ui.add_css('''
    custom-component {
        display: block;
        transition: all 0.3s ease;
    }
    custom-component:hover {
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }
''')
```

## Troubleshooting

### Tailwind Classes Not Applied

**Problem**: Tailwind classes don't seem to work

**Solutions**:
```python
# Ensure classes are properly quoted
ui.button('OK').classes('bg-blue-500 text-white')  # Correct
ui.button('OK').classes(bg-blue-500 text-white)    # Wrong - missing quotes

# Use tailwind() method for complex class strings
ui.element().tailwind('hover:bg-blue-700', 'focus:outline-none', 'focus:ring-2')

# For dynamic classes, use replace parameter
element.classes('bg-red-500', replace='bg-blue-500')
```

### Quasar Props Conflicts

**Problem**: Quasar props override Tailwind styling

**Solution**:
```python
# Quasar's color prop overrides Tailwind background
ui.button('Button', color='primary')  # Quasar wins
ui.button('Button').classes('bg-red-500')  # May be overridden

# Use Quasar's color system instead
ui.button('Button', color='red-5')  # Quasar color scale
ui.button('Button').props('color=red-5')  # Alternative syntax
```

### Dark Mode Issues

**Problem**: Dark mode doesn't affect custom CSS

**Solution**:
```python
# Use CSS classes that respond to dark mode
ui.add_css('''
    .custom-element {
        background-color: white;
        color: black;
    }

    body.dark .custom-element {
        background-color: #1f2937;
        color: white;
    }
''')

# Or use Tailwind's dark: prefix
ui.element().classes('bg-white dark:bg-gray-800 text-black dark:text-white')
```

### CSS Specificity Problems

**Problem**: Styles not applying due to CSS specificity

**Solutions**:
```python
# Use !important in CSS (last resort)
ui.add_css('''
    .override-styles {
        background-color: red !important;
    }
''')

# Or increase specificity
ui.add_css('''
    .q-btn.custom-btn {
        background-color: red;
    }
''')

# Use Tailwind's arbitrary value syntax
ui.button('Button').classes('bg-[#ff0000]')  # Force specific color
```

### Performance with Many Styled Elements

**Problem**: App becomes slow with many styled elements

**Solutions**:
```python
# Define reusable style classes instead of inline styles
ui.add_head_html('''
<style type="text/tailwindcss">
    @layer components {
        .list-item {
            @apply flex items-center p-2 hover:bg-gray-100 dark:hover:bg-gray-800;
        }
    }
</style>
''')

# Apply class instead of individual styling
for item in items:
    ui.label(item).classes('list-item')  # Better performance

# Avoid:
for item in items:
    ui.label(item).classes('flex items-center p-2 hover:bg-gray-100 dark:hover:bg-gray-800')  # Slower
```

## References

- **Official Docs**: https://nicegui.io/documentation/styling
- **Tailwind CSS**: https://tailwindcss.com/docs
- **Quasar Framework**: https://quasar.dev/vue-components
- **CSS Variables**: https://nicegui.io/documentation/custom_CSS_variables
- **Dark Mode**: https://nicegui.io/documentation/dark_mode
- **Colors**: https://nicegui.io/documentation/colors
- **Examples**:
  - `examples/modularization/theme.py` - Theme management
  - `examples/trello_cards/draganddrop.py` - Custom component styling
  - `website/documentation/content/section_styling_appearance.py` - Comprehensive styling demos