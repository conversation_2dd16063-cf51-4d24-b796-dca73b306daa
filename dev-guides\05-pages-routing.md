# NiceGUI Pages & Routing Guide

## Overview

This guide covers NiceGUI's comprehensive routing system for building both traditional multi-page applications and modern single-page applications (SPAs). NiceGUI provides multiple routing approaches: server-side routing with `@ui.page()`, client-side routing with `ui.sub_pages()`, and modular organization with `APIRouter`.

**When to use this guide:**
- Building multi-page applications with distinct URLs
- Creating SPAs with client-side navigation
- Implementing URL parameters and dynamic routing
- Organizing routes across multiple modules
- Managing navigation and page transitions

## Core Concepts

### Server-Side vs Client-Side Routing

**Server-Side Routing (`@ui.page()`):**
- Each route creates a new page instance per user
- Full page reload on navigation
- SEO-friendly with distinct URLs
- Better for traditional web apps

**Client-Side Routing (`ui.sub_pages()`):**
- Single page with dynamic content switching
- No page reload, faster navigation
- Maintains application state
- Better for interactive SPAs

### Page Privacy Model

NiceGUI follows a "private by default" philosophy:
- Pages with `@ui.page()` are private to each user
- Elements outside decorators create shared auto-index pages
- Each client gets their own page instance and state

### URL Structure

NiceGUI supports FastAPI-style URL patterns:
- Static routes: `/about`, `/dashboard`
- Path parameters: `/user/{user_id}`, `/posts/{slug}`
- Query parameters: `/search?q=term&page=1`
- Catch-all routes: `/{path:path}` for SPAs

## Quick Start

### Basic Multi-Page Application

```python
from nicegui import ui

@ui.page('/')
def home():
    ui.label('Welcome to Home')
    ui.link('Go to About', '/about')

@ui.page('/about')
def about():
    ui.label('About Page')
    ui.link('Back to Home', '/')

ui.run()
```

### Single-Page Application with Sub-Pages

```python
from nicegui import ui

@ui.page('/')
@ui.page('/{path:path}')  # Catch all routes
def main():
    with ui.header():
        ui.button('Home', on_click=lambda: ui.navigate.to('/'))
        ui.button('About', on_click=lambda: ui.navigate.to('/about'))
    
    ui.sub_pages({
        '/': home_content,
        '/about': about_content,
    })

def home_content():
    ui.label('Home Content')

def about_content():
    ui.label('About Content')

ui.run()
```

### Path Parameters

```python
@ui.page('/user/{user_id}')
def user_profile(user_id: str):
    ui.label(f'User Profile: {user_id}')

@ui.page('/posts/{post_id}/comments/{comment_id}')
def comment_detail(post_id: int, comment_id: int):
    ui.label(f'Post {post_id}, Comment {comment_id}')
```

## Best Practices

### Route Organization

**DO: Use descriptive route patterns**
```python
@ui.page('/dashboard')
def user_dashboard():
    pass

@ui.page('/settings/profile')
def profile_settings():
    pass

@ui.page('/api/users/{user_id}')
def user_api(user_id: str):
    pass
```

**DO: Group related routes with APIRouter**
```python
from nicegui import APIRouter

# users.py
router = APIRouter(prefix='/users')

@router.page('/')
def user_list():
    pass

@router.page('/{user_id}')
def user_detail(user_id: str):
    pass

# main.py
from nicegui import app
import users
app.include_router(users.router)
```

**DON'T: Mix routing approaches unnecessarily**
```python
# ❌ AVOID: Mixing @ui.page and ui.sub_pages for same routes
@ui.page('/dashboard')
def dashboard():
    ui.sub_pages({'/dashboard': dashboard_content})  # Redundant
```

### Navigation Patterns

**DO: Use ui.navigate.to() for programmatic navigation**
```python
ui.button('Go to Dashboard', 
          on_click=lambda: ui.navigate.to('/dashboard'))

# Navigate to page function directly
ui.button('Settings', 
          on_click=lambda: ui.navigate.to(settings_page))
```

**DO: Use ui.link() for static navigation**
```python
ui.link('About Us', '/about')
ui.link('External Site', 'https://example.com', new_tab=True)
```

**DO: Handle navigation state in SPAs**
```python
def create_navigation():
    current_path = ui.context.client.sub_pages_router.current_path
    
    with ui.row():
        ui.link('Home', '/').classes(
            'text-blue' if current_path == '/' else 'text-gray'
        )
        ui.link('About', '/about').classes(
            'text-blue' if current_path == '/about' else 'text-gray'
        )
```

### Parameter Handling

**DO: Use type hints for automatic conversion**
```python
@ui.page('/product/{product_id}')
def product_detail(product_id: int, category: str = 'all'):
    # product_id automatically converted to int
    # category from query parameter with default
    pass
```

**DO: Access request data when needed**
```python
from fastapi import Request

@ui.page('/api/data')
def api_endpoint(request: Request):
    headers = request.headers
    body = await request.body()
    # Process request data
```

## Common Patterns

### Multi-Page App with Shared Layout

```python
def create_layout(title: str):
    with ui.header():
        ui.label(title).classes('text-h6')
        with ui.row():
            ui.link('Home', '/')
            ui.link('About', '/about')
            ui.link('Contact', '/contact')

@ui.page('/')
def home():
    create_layout('Home')
    with ui.column().classes('p-4'):
        ui.label('Welcome to our website')

@ui.page('/about')
def about():
    create_layout('About')
    with ui.column().classes('p-4'):
        ui.label('About our company')
```

### SPA with Protected Routes

```python
from nicegui import app

def is_authenticated():
    return app.storage.user.get('authenticated', False)

def require_auth(func):
    def wrapper(*args, **kwargs):
        if not is_authenticated():
            show_login_form()
            return
        return func(*args, **kwargs)
    return wrapper

@require_auth
def dashboard_content():
    ui.label('Protected Dashboard Content')

ui.sub_pages({
    '/': public_content,
    '/dashboard': dashboard_content,
})
```

### Dynamic Route Registration

```python
# Register routes from configuration
routes = {
    '/': home_content,
    '/about': about_content,
}

# Add dynamic routes
for category in ['tech', 'science', 'art']:
    routes[f'/category/{category}'] = lambda cat=category: category_content(cat)

ui.sub_pages(routes)
```

### Nested Sub-Pages

```python
def admin_section():
    ui.sub_pages({
        '/admin': admin_dashboard,
        '/admin/users': user_management,
        '/admin/settings': admin_settings,
    })

def main_app():
    ui.sub_pages({
        '/': home_content,
        '/admin': admin_section,  # Nested routing
        '/public': public_section,
    })
```

## Advanced Techniques

### Custom Sub-Pages with Middleware

```python
class AuthenticatedSubPages(ui.sub_pages):
    def _render_page(self, match):
        if self._requires_auth(match.builder) and not self._is_authenticated():
            self._show_login()
            return True
        return super()._render_page(match)
    
    def _requires_auth(self, handler):
        return getattr(handler, '_requires_auth', False)
```

### Page Arguments and Data Passing

```python
from nicegui import PageArguments

def content_with_args(args: PageArguments):
    ui.label(f"Path: {args.path}")
    ui.label(f"Query: {args.query_parameters}")
    ui.label(f"Data: {args.data}")

ui.sub_pages({
    '/content': content_with_args
}, data={'shared_data': 'value'})
```

### Route-Based Component Loading

```python
async def lazy_load_component(component_name: str):
    if component_name == 'chart':
        from .components.chart import create_chart
        return create_chart
    elif component_name == 'table':
        from .components.table import create_table
        return create_table

@ui.page('/component/{name}')
async def component_page(name: str):
    component_func = await lazy_load_component(name)
    if component_func:
        component_func()
    else:
        ui.label('Component not found')
```

### SEO and Meta Tags

```python
@ui.page('/blog/{slug}', 
         title='Blog Post',
         viewport='width=device-width, initial-scale=1')
def blog_post(slug: str):
    # Dynamic title based on content
    post = get_post_by_slug(slug)
    ui.context.client.page.title = post.title
    
    # Add meta tags
    ui.add_head_html(f'''
        <meta name="description" content="{post.description}">
        <meta property="og:title" content="{post.title}">
        <meta property="og:description" content="{post.description}">
    ''')
```

## Integration Points

### FastAPI Integration

```python
from nicegui import app
from fastapi import HTTPException

# Add FastAPI routes alongside NiceGUI pages
@app.get('/api/health')
def health_check():
    return {'status': 'healthy'}

@ui.page('/dashboard')
def dashboard():
    # Can call FastAPI endpoints from NiceGUI pages
    ui.button('Check Health', 
              on_click=lambda: ui.navigate.to('/api/health'))
```

### State Management

```python
# Global state accessible across routes
from nicegui import app

app.storage.general['navigation_history'] = []

def track_navigation(path: str):
    history = app.storage.general.get('navigation_history', [])
    history.append(path)
    app.storage.general['navigation_history'] = history[-10:]  # Keep last 10
```

### Authentication Integration

```python
from fastapi import Depends
from .auth import get_current_user

@ui.page('/protected')
def protected_page(user=Depends(get_current_user)):
    ui.label(f'Welcome, {user.name}')
```

## Troubleshooting

### Common Issues

**Issue: Routes not working in SPA mode**
```python
# ✅ SOLUTION: Ensure catch-all route
@ui.page('/')
@ui.page('/{path:path}')  # This catches all paths
def main():
    ui.sub_pages(routes)
```

**Issue: Page parameters not converting**
```python
# ❌ PROBLEM: Missing type hints
@ui.page('/user/{user_id}')
def user_page(user_id):  # user_id is string
    pass

# ✅ SOLUTION: Add type hints
@ui.page('/user/{user_id}')
def user_page(user_id: int):  # user_id converted to int
    pass
```

**Issue: Navigation not updating URL**
```python
# ✅ SOLUTION: Use ui.navigate.to() instead of direct element manipulation
ui.button('Go', on_click=lambda: ui.navigate.to('/target'))
```

**Issue: Shared state between users**
```python
# ❌ PROBLEM: Global variables shared between users
user_data = {}  # Shared across all users

# ✅ SOLUTION: Use app.storage for user-specific data
@ui.page('/profile')
def profile():
    user_data = app.storage.user.get('profile', {})
```

### Performance Considerations

- Use `ui.sub_pages()` for frequently navigated sections
- Implement lazy loading for heavy components
- Consider route-based code splitting for large applications
- Cache static route data when possible

### Browser Compatibility

- Client-side routing requires JavaScript enabled
- Use server-side routing for better SEO and accessibility
- Test navigation with browser back/forward buttons
- Ensure proper handling of page refresh in SPAs

## References

- **Official Documentation:** https://nicegui.io/documentation/section_pages_routing
- **Sub-Pages Documentation:** https://nicegui.io/documentation/sub_pages
- **FastAPI Routing:** https://fastapi.tiangolo.com/tutorial/path-params/
- **Examples:**
  - Single Page App: `/examples/single_page_app/`
  - Modularization: `/examples/modularization/`
  - Authentication: `/examples/authentication/`
- **Related Guides:**
  - 02-nicegui-fundamentals.md for basic concepts
  - 07-events-interactivity.md for navigation events
  - 09-authentication-security.md for protected routes
