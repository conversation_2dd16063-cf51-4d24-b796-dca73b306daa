# Guide #09: Authentication & Security

## Overview

This guide covers implementing authentication systems, session management, and security best practices in NiceGUI applications. NiceGUI leverages FastAPI's security features while providing its own storage and session management capabilities.

**When to use this guide:**

- Building apps requiring user authentication
- Implementing role-based access control
- Securing sensitive data and operations
- Adding OAuth/SSO integration
- Protecting against common web vulnerabilities

## Core Concepts

### NiceGUI Security Architecture

NiceGUI applications run on FastAPI, inheriting its security model:

- **Session-based authentication** via `app.storage.user`
- **Middleware-based protection** for route access control
- **Storage secrets** for secure browser sessions
- **CORS configuration** through SocketIO settings
- **Input validation** through element validation systems

### Storage & Sessions

```python
# Enable secure sessions
ui.run(storage_secret='your-secret-key-here')

# User-specific storage (requires storage_secret)
app.storage.user['authenticated'] = True
app.storage.user['username'] = 'john_doe'
app.storage.user['roles'] = ['admin', 'user']

# General storage (shared across all users)
app.storage.general['login_attempts'] = {}
```

### Authentication Flow

1. **Request arrives** → Middleware checks authentication
2. **Unauthenticated users** → Redirect to login page
3. **Login success** → Store user data in `app.storage.user`
4. **Protected routes** → Verify authentication status
5. **Logout** → Clear user storage and redirect

## Quick Start

### Basic Authentication System

```python
from fastapi import Request
from fastapi.responses import RedirectResponse
from starlette.middleware.base import BaseHTTPMiddleware
from nicegui import app, ui

# Simple user database (use proper database in production)
users = {'admin': 'password123', 'user': 'secret456'}
unrestricted_routes = {'/login', '/register'}

class AuthMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        if not app.storage.user.get('authenticated', False):
            if (not request.url.path.startswith('/_nicegui') and
                request.url.path not in unrestricted_routes):
                return RedirectResponse(f'/login?redirect_to={request.url.path}')
        return await call_next(request)

app.add_middleware(AuthMiddleware)

@ui.page('/login')
def login_page(redirect_to: str = '/'):
    def try_login():
        if users.get(username.value) == password.value:
            app.storage.user.update({
                'username': username.value,
                'authenticated': True
            })
            ui.navigate.to(redirect_to)
        else:
            ui.notify('Invalid credentials', color='negative')

    if app.storage.user.get('authenticated', False):
        return RedirectResponse('/')

    with ui.card().classes('absolute-center'):
        ui.label('Login').classes('text-h6')
        username = ui.input('Username').on('keydown.enter', try_login)
        password = ui.input('Password', password=True).on('keydown.enter', try_login)
        ui.button('Login', on_click=try_login)

@ui.page('/')
def main_page():
    def logout():
        app.storage.user.clear()
        ui.navigate.to('/login')

    ui.label(f'Welcome {app.storage.user["username"]}!')
    ui.button('Logout', on_click=logout)

ui.run(storage_secret='change-this-secret-key')
```

## Best Practices

### Security Fundamentals

**1. Always Use Storage Secrets**

```python
# Required for secure sessions
ui.run(storage_secret=os.environ.get('NICEGUI_SECRET', 'fallback-secret'))
```

**2. Hash Passwords**

```python
import bcrypt

def hash_password(password: str) -> str:
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

def verify_password(password: str, hashed: str) -> bool:
    return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
```

**3. Validate Input**

```python
def validate_username(username: str) -> str:
    if not username or len(username) < 3:
        return 'Username must be at least 3 characters'
    if not username.isalnum():
        return 'Username must contain only letters and numbers'
    return ''

username = ui.input('Username', validation=validate_username)
```

**4. Implement Rate Limiting**

```python
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.errors import RateLimitExceeded
from slowapi.middleware import SlowAPIMiddleware
from slowapi.util import get_remote_address

limiter = Limiter(key_func=get_remote_address, default_limits=['10/minute'])
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)
app.add_middleware(SlowAPIMiddleware)

@app.post('/login')
@limiter.limit('5/minute')
async def login_endpoint(request: Request):
    # Login logic here
    pass
```

### Anti-Patterns to Avoid

❌ **Don't store passwords in plain text**
❌ **Don't use weak session secrets**
❌ **Don't trust client-side validation only**
❌ **Don't expose sensitive data in URLs**
❌ **Don't skip HTTPS in production**

## Common Patterns

### Role-Based Access Control

```python
def require_role(required_role: str):
    def decorator(func):
        def wrapper(*args, **kwargs):
            user_roles = app.storage.user.get('roles', [])
            if required_role not in user_roles:
                ui.notify('Access denied', color='negative')
                return
            return func(*args, **kwargs)
        return wrapper
    return decorator

@require_role('admin')
def admin_panel():
    ui.label('Admin Panel')
    ui.button('Delete All Users', color='negative')
```

### OAuth Integration (Google)

```python
from authlib.integrations.starlette_client import OAuth

oauth = OAuth()
oauth.register(
    name='google',
    server_metadata_url='https://accounts.google.com/.well-known/openid-configuration',
    client_id='your-google-client-id',
    client_secret='your-google-client-secret',
    client_kwargs={'scope': 'openid email profile'},
)

@ui.page('/')
async def main(request: Request):
    user_info = app.storage.user.get('user_info', {})
    if not user_info:
        return await oauth.google.authorize_redirect(request, request.url_for('oauth_callback'))

    ui.label(f'Welcome {user_info.get("name")}!')

@app.get('/auth/callback')
async def oauth_callback(request: Request):
    try:
        user_info = (await oauth.google.authorize_access_token(request)).get('userinfo', {})
        app.storage.user['user_info'] = user_info
    except Exception:
        ui.notify('Authentication failed', color='negative')
    return RedirectResponse('/')
```

### Session Management

```python
from datetime import datetime, timedelta

def extend_session():
    """Extend user session on activity"""
    app.storage.user['last_activity'] = datetime.now().isoformat()

def check_session_timeout():
    """Check if session has expired"""
    last_activity = app.storage.user.get('last_activity')
    if not last_activity:
        return True

    last_time = datetime.fromisoformat(last_activity)
    return datetime.now() - last_time > timedelta(hours=24)

# Add to middleware
class SessionTimeoutMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        if app.storage.user.get('authenticated') and check_session_timeout():
            app.storage.user.clear()
            return RedirectResponse('/login?reason=timeout')

        if app.storage.user.get('authenticated'):
            extend_session()

        return await call_next(request)
```

### Protected Sub-Pages

```python
def protected(func):
    """Decorator for protected route handlers"""
    func._is_protected = True
    return func

class AuthenticatedSubPages(ui.sub_pages):
    def _render_page(self, match):
        if (hasattr(match.builder, '_is_protected') and
            not app.storage.user.get('authenticated', False)):
            self._show_login_form()
            return True
        return super()._render_page(match)

    def _show_login_form(self):
        ui.navigate.to('/login')

# Usage
@protected
def admin_content():
    ui.label('Admin Dashboard')

ui.sub_pages({
    '/': public_content,
    '/admin': admin_content,
})
```

### Input Sanitization

```python
import html
import re

def sanitize_input(value: str) -> str:
    """Sanitize user input to prevent XSS"""
    if not value:
        return ''

    # HTML escape
    value = html.escape(value)

    # Remove potentially dangerous patterns
    value = re.sub(r'javascript:', '', value, flags=re.IGNORECASE)
    value = re.sub(r'on\w+\s*=', '', value, flags=re.IGNORECASE)

    return value.strip()

def validate_email(email: str) -> str:
    """Validate email format"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(pattern, email):
        return 'Invalid email format'
    return ''

# Use in forms
email = ui.input('Email', validation=validate_email)
comment = ui.textarea('Comment',
                     on_change=lambda e: setattr(e.sender, 'value',
                                                sanitize_input(e.value)))
```

## Advanced Techniques

### Custom Authentication Provider

```python
from abc import ABC, abstractmethod

class AuthProvider(ABC):
    @abstractmethod
    async def authenticate(self, username: str, password: str) -> dict:
        pass

    @abstractmethod
    async def get_user_info(self, user_id: str) -> dict:
        pass

class DatabaseAuthProvider(AuthProvider):
    def __init__(self, db_connection):
        self.db = db_connection

    async def authenticate(self, username: str, password: str) -> dict:
        # Database lookup and password verification
        user = await self.db.fetch_user(username)
        if user and verify_password(password, user['password_hash']):
            return {'user_id': user['id'], 'username': user['username']}
        return {}

    async def get_user_info(self, user_id: str) -> dict:
        return await self.db.fetch_user_by_id(user_id)

# Usage
auth_provider = DatabaseAuthProvider(db_connection)

async def login_handler():
    user_data = await auth_provider.authenticate(username.value, password.value)
    if user_data:
        app.storage.user.update(user_data)
        ui.navigate.to('/')
    else:
        ui.notify('Invalid credentials', color='negative')
```

### JWT Token Authentication

```python
import jwt
from datetime import datetime, timedelta

SECRET_KEY = "your-jwt-secret"
ALGORITHM = "HS256"

def create_access_token(data: dict, expires_delta: timedelta = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)

    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(token: str):
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            return None
        return payload
    except jwt.PyJWTError:
        return None

# Middleware for JWT validation
class JWTMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        token = request.headers.get("Authorization")
        if token and token.startswith("Bearer "):
            token = token[7:]
            payload = verify_token(token)
            if payload:
                app.storage.user.update(payload)

        return await call_next(request)
```

### Multi-Factor Authentication

```python
import pyotp
import qrcode
from io import BytesIO
import base64

def generate_mfa_secret(username: str) -> str:
    """Generate MFA secret for user"""
    secret = pyotp.random_base32()
    app.storage.user[f'mfa_secret_{username}'] = secret
    return secret

def generate_qr_code(username: str, secret: str) -> str:
    """Generate QR code for MFA setup"""
    totp_uri = pyotp.totp.TOTP(secret).provisioning_uri(
        name=username,
        issuer_name="Your App Name"
    )

    qr = qrcode.QRCode(version=1, box_size=10, border=5)
    qr.add_data(totp_uri)
    qr.make(fit=True)

    img = qr.make_image(fill_color="black", back_color="white")
    buffer = BytesIO()
    img.save(buffer, format='PNG')
    buffer.seek(0)

    return base64.b64encode(buffer.getvalue()).decode()

def verify_mfa_token(username: str, token: str) -> bool:
    """Verify MFA token"""
    secret = app.storage.user.get(f'mfa_secret_{username}')
    if not secret:
        return False

    totp = pyotp.TOTP(secret)
    return totp.verify(token)

# MFA setup page
@ui.page('/setup-mfa')
def setup_mfa():
    username = app.storage.user.get('username')
    if not username:
        ui.navigate.to('/login')
        return

    secret = generate_mfa_secret(username)
    qr_data = generate_qr_code(username, secret)

    ui.label('Scan this QR code with your authenticator app:')
    ui.html(f'<img src="data:image/png;base64,{qr_data}" />')

    token_input = ui.input('Enter verification code')

    def verify_setup():
        if verify_mfa_token(username, token_input.value):
            app.storage.user['mfa_enabled'] = True
            ui.notify('MFA setup successful!', color='positive')
            ui.navigate.to('/')
        else:
            ui.notify('Invalid code', color='negative')

    ui.button('Verify', on_click=verify_setup)
```

## Integration Points

### FastAPI Security Integration

```python
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials

security = HTTPBearer()

def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """FastAPI dependency for protected endpoints"""
    payload = verify_token(credentials.credentials)
    if payload is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return payload

# Protected FastAPI endpoint
@app.get('/api/protected')
async def protected_endpoint(current_user: dict = Depends(get_current_user)):
    return {"message": f"Hello {current_user['username']}"}

# Use in NiceGUI pages
@ui.page('/api-test')
async def api_test():
    token = app.storage.user.get('access_token')
    if not token:
        ui.label('Please login first')
        return

    async with httpx.AsyncClient() as client:
        response = await client.get(
            '/api/protected',
            headers={'Authorization': f'Bearer {token}'}
        )

    if response.status_code == 200:
        ui.json(response.json())
    else:
        ui.label('Access denied')
```

### Database Integration

```python
import asyncpg
from contextlib import asynccontextmanager

class UserRepository:
    def __init__(self, db_pool):
        self.pool = db_pool

    async def create_user(self, username: str, email: str, password_hash: str):
        async with self.pool.acquire() as conn:
            return await conn.fetchrow(
                "INSERT INTO users (username, email, password_hash) VALUES ($1, $2, $3) RETURNING id",
                username, email, password_hash
            )

    async def get_user_by_username(self, username: str):
        async with self.pool.acquire() as conn:
            return await conn.fetchrow(
                "SELECT * FROM users WHERE username = $1", username
            )

    async def update_last_login(self, user_id: int):
        async with self.pool.acquire() as conn:
            await conn.execute(
                "UPDATE users SET last_login = NOW() WHERE id = $1", user_id
            )

# Initialize database connection
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    app.state.db_pool = await asyncpg.create_pool(
        "postgresql://user:password@localhost/dbname"
    )
    app.state.user_repo = UserRepository(app.state.db_pool)
    yield
    # Shutdown
    await app.state.db_pool.close()

# Use with FastAPI
from fastapi import FastAPI
fastapi_app = FastAPI(lifespan=lifespan)

# Access in NiceGUI
async def login_with_db():
    user_repo = app.state.user_repo
    user = await user_repo.get_user_by_username(username.value)
    if user and verify_password(password.value, user['password_hash']):
        await user_repo.update_last_login(user['id'])
        app.storage.user.update({
            'user_id': user['id'],
            'username': user['username'],
            'authenticated': True
        })
        ui.navigate.to('/')
```

## Troubleshooting

### Common Issues

**1. Sessions Not Persisting**

```python
# Problem: Missing storage_secret
ui.run()  # ❌ Sessions won't persist

# Solution: Add storage_secret
ui.run(storage_secret='your-secret-key')  # ✅
```

**2. Middleware Order Issues**

```python
# Problem: Wrong middleware order
app.add_middleware(AuthMiddleware)
app.add_middleware(SessionMiddleware, secret_key='secret')  # ❌

# Solution: Correct order (last added = first executed)
app.add_middleware(SessionMiddleware, secret_key='secret')
app.add_middleware(AuthMiddleware)  # ✅
```

**3. CORS Issues with Authentication**

```python
# Problem: CORS blocking authentication requests
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,  # ✅ Required for auth cookies
    allow_methods=["*"],
    allow_headers=["*"],
)
```

**4. Storage Access in Middleware**

```python
# Problem: Storage not available in middleware
class AuthMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # app.storage.user not available here ❌
        return await call_next(request)

# Solution: Use request.session directly
class AuthMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        if 'id' not in request.session:
            return RedirectResponse('/login')
        # Check authentication in session ✅
        return await call_next(request)
```

### Security Checklist

✅ **Use HTTPS in production**
✅ **Set strong storage_secret**
✅ **Hash passwords with salt**
✅ **Validate all user inputs**
✅ **Implement rate limiting**
✅ **Use secure session cookies**
✅ **Sanitize output to prevent XSS**
✅ **Implement proper logout**
✅ **Use CSRF protection for forms**
✅ **Log security events**

### Performance Considerations

```python
# Cache user data to reduce database calls
from functools import lru_cache

@lru_cache(maxsize=1000)
def get_user_permissions(user_id: int) -> list:
    # Expensive database call
    return fetch_user_permissions(user_id)

# Clear cache on permission changes
def update_user_permissions(user_id: int, new_permissions: list):
    get_user_permissions.cache_clear()
    # Update database
```

## References

- **Official Docs**: [NiceGUI Storage](https://nicegui.io/documentation/storage)
- **Examples**:
  - `examples/authentication/` - Basic auth with middleware
  - `examples/google_oauth2/` - OAuth integration
  - `examples/descope_auth/` - Third-party auth service
- **FastAPI Security**: [FastAPI Security Tutorial](https://fastapi.tiangolo.com/tutorial/security/)
- **Related Guides**:
  - Guide #08: Data Storage & Persistence
  - Guide #05: Pages & Routing
  - Guide #12: Performance & Deployment
