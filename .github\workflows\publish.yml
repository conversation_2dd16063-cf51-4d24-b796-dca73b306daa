name: Publish Release

on:
  workflow_dispatch:
  push:
    tags:
      - v**

jobs:
  pypi:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5
      - name: set up Python
        uses: actions/setup-python@v6
        with:
          python-version: "3.x"
      - name: set up Poetry
        uses: abatilo/actions-poetry@v4
      - name: get version
        run: echo "VERSION=${GITHUB_REF#refs/tags/}" >> $GITHUB_ENV
      - name: Set version
        run: poetry version ${{ env.VERSION }}
      - name: publish
        env:
          POETRY_PYPI_TOKEN_PYPI: ${{ secrets.PYPI_API_TOKEN }}
        run: poetry publish --build
      - name: Create GitHub release entry
        uses: softprops/action-gh-release@v2
        with:
          draft: true
          prerelease: false
          name: ${{ env.VERSION }}
          tag_name: ${{ env.VERSION }}
        env:
          GITHUB_TOKEN: ${{ github.token }}
      - name: verify
        shell: bash
        run: for i in {1..100}; do sleep 2; python -m pip install 'nicegui==${{ env.VERSION }}' && break; done; sleep 5

  docker:
    needs: pypi
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5
      - name: Prepare
        id: prep
        run: |
          DOCKER_IMAGE=${{ secrets.DOCKER_USERNAME }}/${GITHUB_REPOSITORY#*/}
          VERSION=latest
          SHORTREF=${GITHUB_SHA::8}

          # If this is git tag, use the tag name as a docker tag
          if [[ $GITHUB_REF == refs/tags/* ]]; then
            VERSION=${GITHUB_REF#refs/tags/v}
          fi
          TAGS="${DOCKER_IMAGE}:${VERSION},${DOCKER_IMAGE}:${SHORTREF}"

          # If the VERSION looks like a version number, assume that
          # this is the most recent version of the image and also
          # tag it 'latest'.
          if [[ $VERSION =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
            TAGS="$TAGS,${DOCKER_IMAGE}:latest"
          fi

          # Set output parameters.
          echo "tags=${TAGS}" >> $GITHUB_OUTPUT
          echo "docker_image=${DOCKER_IMAGE}" >> $GITHUB_OUTPUT
          echo "version=${VERSION}" >> $GITHUB_OUTPUT
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
      - name: Login to DockerHub
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Build and push
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./release.dockerfile
          platforms: linux/amd64, linux/arm64
          push: true
          tags: ${{ steps.prep.outputs.tags }}
          build-args: VERSION=${{ steps.prep.outputs.version }}
          cache-from: |
            type=registry,ref=${{ steps.prep.outputs.docker_image }}:buildcache-amd64
            type=registry,ref=${{ steps.prep.outputs.docker_image }}:buildcache-arm64
      - name: Cache linux/amd64 to registry
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./release.dockerfile
          platforms: linux/amd64
          build-args: VERSION=${{ steps.prep.outputs.version }}
          push: false
          cache-from: type=registry,ref=${{ steps.prep.outputs.docker_image }}:buildcache-amd64
          cache-to: type=registry,ref=${{ steps.prep.outputs.docker_image }}:buildcache-amd64,mode=max
      - name: Cache linux/arm64 to registry
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./release.dockerfile
          platforms: linux/arm64
          build-args: VERSION=${{ steps.prep.outputs.version }}
          push: false
          cache-from: type=registry,ref=${{ steps.prep.outputs.docker_image }}:buildcache-arm64
          cache-to: type=registry,ref=${{ steps.prep.outputs.docker_image }}:buildcache-arm64,mode=max
      # Uploading the README.md is not a core feature of docker/build-push-action yet
      - name: Update README
        uses: christian-korneck/update-container-description-action@v1
        env:
          DOCKER_USER: ${{ secrets.DOCKER_USERNAME }}
          DOCKER_PASS: ${{ secrets.DOCKER_PASSWORD }}
        with:
          destination_container_repo: zauberzeug/nicegui
          provider: dockerhub
          short_description: "Web Based User Interface for Python with Buttons, Dialogs, Markdown, 3D Scenes and Plots"

  update_version:
    needs: docker
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5
        with:
          ref: main
      - uses: actions/setup-python@v6
        with:
          python-version: "3.11"
      - name: Install httpx
        run: pip install httpx
      - name: Update version in pyproject.toml and citation.cff
        run: python .github/workflows/update_version.py ${GITHUB_REF#refs/tags/}
      - name: Commit and push changes
        run: |
          git config --global user.name "github-actions[bot]"
          git config --global user.email "41898282+github-actions[bot]@users.noreply.github.com"
          git add pyproject.toml CITATION.cff
          git commit -m "Update version"
          git push origin HEAD:main

  verify:
    needs: docker
    runs-on: ubuntu-latest
    steps:
      - name: Pull and Test Container
        env:
          DOCKER_IMAGE: zauberzeug/nicegui
          VERSION: latest
        run: |
          docker pull ${DOCKER_IMAGE}:${VERSION}
          docker run -d --name test_container ${DOCKER_IMAGE}:${VERSION}
          sleep 10
          CONTAINER_OUTPUT=$(docker logs test_container)
          # Check if the container is still running
          CONTAINER_STATUS=$(docker inspect -f '{{.State.Running}}' test_container)
          if [ "${CONTAINER_STATUS}" != "true" ]; then
            echo "The container is not running!"
            exit 1
          fi
          # Check if the "Error" string is present in the container output
          if echo "${CONTAINER_OUTPUT}" | grep -q "Error"; then
            echo "Error found in container output!"
            echo "${CONTAINER_OUTPUT}"
            exit 1
          fi
          docker stop test_container
          docker rm test_container
