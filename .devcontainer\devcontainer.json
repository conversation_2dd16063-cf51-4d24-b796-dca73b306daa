// For format details, see https://aka.ms/devcontainer.json.
{
  "name": "nicegui-dev",
  "build": {
    "context": "..",
    "dockerfile": "Dockerfile"
  },
  "customizations": {
    "vscode": {
      "extensions": [
        "charliermarsh.ruff",
        "cschleiden.vscode-github-actions",
        "esbenp.prettier-vscode",
        "littlefoxteam.vscode-python-test-adapter",
        "ms-python.autopep8",
        "ms-python.mypy-type-checker",
        "ms-python.pylint",
        "ms-python.python",
        "ms-python.vscode-pylance",
        "samuelcolvin.jinjahtml",
        "Vue.volar"
      ],
      "settings": {
        "ruff.enable": true,
        "terminal.integrated.defaultProfile.linux": "bash",
        "terminal.integrated.shell.linux": "bash",
        "terminal.integrated.profiles.linux": {
          "bash (container default)": {
            "path": "/usr/bin/bash",
            "overrideName": true
          }
        }
      }
    }
  },
  // More info: https://aka.ms/dev-containers-non-root.
  "remoteUser": "vscode",
  "postCreateCommand": "poetry install --all-extras"
}
