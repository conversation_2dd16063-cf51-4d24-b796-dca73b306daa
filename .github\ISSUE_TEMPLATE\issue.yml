name: Report an issue
description: Report a bug or some other unexpected behavior.
body:
  - type: markdown
    attributes:
      value: |
        Thanks for your interest in NiceGUI! 🚀

        Please follow these instructions, fill every question, and do every step. 🙏

        We're asking this because reading, reproducing and replying to issues consumes a lot of the time.

        By reporting issues in a structured way (following this) it will be much easier to help.
  - type: checkboxes
    id: checks
    attributes:
      label: First Check
      description: Please confirm and check all the following options.
      options:
        - label: I added a very descriptive title here.
          required: true
        - label: This is not a Q&A. I am sure something is wrong with NiceGUI or its documentation.
          required: true
        - label: I used the GitHub search to find a similar [issue](https://github.com/zauberzeug/nicegui/issues) and came up empty.
          required: true
  - type: textarea
    id: example
    attributes:
      label: Example Code
      description: |
        If applicable, please add a self-contained, [minimal, reproducible, example](https://stackoverflow.com/help/minimal-reproducible-example) with your use case.

        If one can copy it, run it, and see it right away, there's a much higher chance we (or someone from the community) will be able reproduce and fix the problem.
      placeholder: |
        from nicegui import ui

        ui.label('Hello, world!')

        ui.run()
      render: python
  - type: textarea
    id: description
    attributes:
      label: Description
      description: |
        What is the problem, question, or error?

        Write a short description telling us what you are doing, what you expect to happen, and what is currently happening.
      placeholder: |
        Start the app.
        The browser opens and shows a label with text "Hello, world!".
        But I expected it to show "Hello, Alice!".
    validations:
      required: true
  - type: input
    id: nicegui-version
    attributes:
      label: NiceGUI Version
      description: |
        What NiceGUI version are you using?

        You can find the NiceGUI version with:

        ```bash
        python -c "import nicegui; print(nicegui.__version__)"
        ```
    validations:
      required: true
  - type: input
    id: python-version
    attributes:
      label: Python Version
      description: |
        What Python version are you using?

        You can find the Python version with:

        ```bash
        python --version
        ```
    validations:
      required: true
  - type: dropdown
    id: browser
    attributes:
      label: Browser
      description: What browser have you tested with?
      multiple: true
      options:
        - Chrome
        - Safari
        - Edge
        - Firefox
        - Opera
        - Samsung Internet
        - Other
    validations:
      required: true
  - type: dropdown
    id: os
    attributes:
      label: Operating System
      description: What operating system are you on?
      options:
        - Linux
        - macOS
        - Windows
        - Android
        - iOS
        - Other
    validations:
      required: true
  - type: textarea
    id: context
    attributes:
      label: Additional Context
      description: Add any additional context information or screenshots you think are useful.
