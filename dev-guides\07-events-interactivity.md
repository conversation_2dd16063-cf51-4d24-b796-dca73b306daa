# Guide #07: Events & Interactivity

## Overview

This guide covers NiceGUI's comprehensive event system and interactivity patterns. NiceGUI provides a powerful event-driven architecture that enables real-time, responsive user interfaces through Python-first development. You'll learn to handle user interactions, implement data binding, manage application state, and create real-time features using WebSocket communication.

**When to use this guide:**

- Building interactive forms and user interfaces
- Implementing real-time data updates and live streaming
- Creating responsive applications with complex user interactions
- Managing application state across components and sessions
- Developing collaborative or multi-user applications

## Core Concepts

### Event-Driven Architecture

NiceGUI uses a **backend-first event system** where:

- All events are handled in Python on the server
- Real-time bidirectional communication via WebSocket (socket.io)
- Automatic synchronization between frontend and backend state
- No JavaScript knowledge required for most interactions

### Event Flow Pattern

```
User Action → Frontend Event → WebSocket → Python Handler → State Update → UI Refresh
```

### Key Event Types

1. **Click Events**: Button clicks, element interactions
2. **Value Change Events**: Input fields, form controls, selections
3. **Keyboard Events**: Key presses, shortcuts, navigation
4. **Custom Events**: Component-specific interactions
5. **System Events**: Connection, disconnection, lifecycle events

## Quick Start

### Basic Event Handling

```python
from nicegui import ui

@ui.page('/')
def interactive_demo():
    # Simple click event
    ui.button('Click me', on_click=lambda: ui.notify('Hello!'))

    # Input with change event
    result = ui.label('Type something...')
    ui.input('Enter text', on_change=lambda e: result.set_text(f'You typed: {e.value}'))

    # Async event handler
    async def save_data():
        ui.notify('Saving...', type='ongoing')
        await asyncio.sleep(1)  # Simulate async operation
        ui.notify('Saved!', type='positive')

    ui.button('Save', on_click=save_data)

ui.run()
```

### Data Binding Basics

```python
from dataclasses import dataclass
from nicegui import ui

@dataclass
class UserData:
    name: str = ''
    email: str = ''
    active: bool = True

user = UserData()

@ui.page('/')
def binding_demo():
    # Two-way data binding
    ui.input('Name').bind_value(user, 'name')
    ui.input('Email').bind_value(user, 'email')
    ui.checkbox('Active').bind_value(user, 'active')

    # Display bound data
    ui.json_editor(user).props('readonly')

ui.run()
```

## Best Practices

### Event Handler Patterns

**DO: Use lambda for simple actions**

```python
ui.button('Notify', on_click=lambda: ui.notify('Success!'))
```

**DO: Use async functions for complex operations**

```python
async def handle_upload(e):
    ui.notify('Processing...', type='ongoing')
    result = await process_file(e.content)
    ui.notify(f'Processed: {result}', type='positive')

ui.upload(on_upload=handle_upload)
```

**DO: Use parameterized handlers for dynamic content**

```python
def create_item_handler(item_id: int):
    return lambda: ui.notify(f'Selected item {item_id}')

for i in range(5):
    ui.button(f'Item {i}', on_click=create_item_handler(i))
```

### State Management Patterns

**DO: Use reactive state with @ui.refreshable**

```python
@ui.refreshable
def counter_display():
    ui.label(f'Count: {app.storage.user.get("count", 0)}')

@ui.page('/')
def counter_app():
    counter_display()

    def increment():
        current = app.storage.user.get('count', 0)
        app.storage.user['count'] = current + 1
        counter_display.refresh()

    ui.button('Increment', on_click=increment)
```

**DO: Leverage app.storage for persistence**

```python
@ui.page('/')
def persistent_form():
    # User-specific storage (survives sessions)
    ui.input('Username').bind_value(app.storage.user, 'username')

    # Browser storage (survives page refresh)
    ui.checkbox('Remember me').bind_value(app.storage.browser, 'remember')

    # Tab storage (volatile, per-tab)
    ui.input('Search').bind_value(app.storage.tab, 'search_term')
```

### Data Binding Best Practices

**DO: Use bindable properties for custom classes**

```python
from nicegui.binding import BindableProperty

class AppState:
    count = BindableProperty()

    def __init__(self):
        self.count = 0

state = AppState()
ui.label().bind_text_from(state, 'count', lambda x: f'Count: {x}')
ui.button('Increment', on_click=lambda: setattr(state, 'count', state.count + 1))
```

## Common Patterns

### Form Handling with Validation

```python
class UserForm:
    def __init__(self):
        self.data = {
            'name': '',
            'email': '',
            'age': 0,
            'role': 'user'
        }
        self.errors = {}

    def validate(self):
        self.errors.clear()
        if not self.data['name'].strip():
            self.errors['name'] = 'Name is required'
        if '@' not in self.data['email']:
            self.errors['email'] = 'Invalid email'
        if self.data['age'] < 0:
            self.errors['age'] = 'Age must be positive'
        return len(self.errors) == 0

    def create_form(self):
        with ui.card().classes('w-full max-w-md mx-auto p-6'):
            ui.input(
                'Name',
                validation={'Required': lambda x: len(x.strip()) > 0}
            ).bind_value(self.data, 'name')

            ui.input(
                'Email',
                validation={'Invalid email': lambda x: '@' in x and '.' in x}
            ).bind_value(self.data, 'email')

            ui.number('Age', min=0).bind_value(self.data, 'age')

            ui.select(['admin', 'user'], label='Role').bind_value(self.data, 'role')

            ui.button('Submit', on_click=self.submit)

    def submit(self):
        if self.validate():
            ui.notify(f'Form submitted: {self.data}', type='positive')
        else:
            ui.notify('Please fix validation errors', type='negative')

form = UserForm()
form.create_form()
```

### Real-Time Data Updates

```python
import asyncio
from datetime import datetime

# Global data store
live_data = {'temperature': 20.0, 'humidity': 45.0}

@ui.refreshable
def sensor_display():
    with ui.card().classes('p-4'):
        ui.label(f"Temperature: {live_data['temperature']:.1f}°C").classes('text-h6')
        ui.label(f"Humidity: {live_data['humidity']:.1f}%").classes('text-h6')
        ui.label(f"Updated: {datetime.now().strftime('%H:%M:%S')}").classes('text-caption')

async def update_sensors():
    """Simulate real-time sensor data updates"""
    import random
    while True:
        live_data['temperature'] = 20 + random.uniform(-5, 5)
        live_data['humidity'] = 45 + random.uniform(-10, 10)
        sensor_display.refresh()
        await asyncio.sleep(2)

@ui.page('/')
def dashboard():
    sensor_display()
    ui.button('Start Updates', on_click=lambda: asyncio.create_task(update_sensors()))

ui.run()
```

### Keyboard Event Handling

```python
from nicegui.events import KeyEventArguments

def handle_key(e: KeyEventArguments):
    if e.key == 'Enter' and e.modifiers.ctrl:
        ui.notify('Ctrl+Enter pressed!')
    elif e.key.arrow_left and e.modifiers.shift:
        ui.notify('Shift+Left Arrow')
    elif e.action.keydown and not e.action.repeat:
        ui.notify(f'Key pressed: {e.key.name}')

@ui.page('/')
def keyboard_demo():
    ui.keyboard(on_key=handle_key)
    ui.label('Press keys to see events (Ctrl+Enter, Shift+Arrows, etc.)')

    # Element-specific keyboard events
    search_input = ui.input('Search')
    search_input.on('keydown.enter', lambda: ui.notify('Search triggered!'))
    search_input.on('keydown.escape', lambda: search_input.set_value(''))

ui.run()
```

### Custom Event Patterns

```python
# Custom event handling for complex components
def create_interactive_table():
    data = [
        {'id': 1, 'name': 'Alice', 'age': 25},
        {'id': 2, 'name': 'Bob', 'age': 30},
        {'id': 3, 'name': 'Carol', 'age': 35}
    ]

    def handle_row_click(e):
        row_data = e.args
        ui.notify(f'Clicked row: {row_data["name"]}')

    def handle_cell_edit(e):
        ui.notify(f'Edited: {e.args}')

    table = ui.table(
        columns=[
            {'name': 'name', 'label': 'Name', 'field': 'name'},
            {'name': 'age', 'label': 'Age', 'field': 'age'}
        ],
        rows=data,
        row_key='id'
    )

    # Subscribe to custom events
    table.on('rowClick', handle_row_click)
    table.on('cellEdit', handle_cell_edit)

    return table

create_interactive_table()
```

## Advanced Techniques

### State Synchronization Across Clients

```python
# Shared state management for multi-user applications
class SharedState:
    def __init__(self):
        self.data = app.storage.general.setdefault('shared_data', {
            'counter': 0,
            'messages': []
        })

    def increment_counter(self):
        self.data['counter'] += 1
        self.notify_all_clients()

    def add_message(self, message: str):
        self.data['messages'].append({
            'text': message,
            'timestamp': datetime.now().isoformat()
        })
        self.notify_all_clients()

    def notify_all_clients(self):
        # Refresh all connected clients
        for client in Client.instances.values():
            if hasattr(client, 'refresh_shared_state'):
                client.refresh_shared_state()

shared_state = SharedState()

@ui.refreshable
def shared_display():
    ui.label(f"Global Counter: {shared_state.data['counter']}")
    with ui.column():
        for msg in shared_state.data['messages'][-5:]:  # Show last 5 messages
            ui.label(f"{msg['timestamp']}: {msg['text']}")

@ui.page('/')
def collaborative_app():
    # Store refresh function on client for global access
    ui.context.client.refresh_shared_state = shared_display.refresh

    shared_display()

    ui.button('Increment Global Counter',
              on_click=shared_state.increment_counter)

    message_input = ui.input('Add message')
    message_input.on('keydown.enter',
                     lambda: shared_state.add_message(message_input.value))

ui.run()
```

### WebSocket Communication & Real-Time Features

```python
# Timer-based real-time updates
@ui.page('/')
def live_dashboard():
    # Live clock
    clock_label = ui.label()
    ui.timer(1.0, lambda: clock_label.set_text(datetime.now().strftime('%H:%M:%S')))

    # Live data streaming
    data_points = []
    chart = ui.line_plot(n=1, limit=50, figsize=(10, 4))

    def update_chart():
        import random
        value = random.uniform(0, 100)
        data_points.append(value)
        chart.push([datetime.now()], [[value]])

    ui.timer(0.5, update_chart)  # Update every 500ms

    # Push notifications
    def send_notification():
        ui.notify('Real-time notification!', type='info')

    ui.timer(10.0, send_notification)  # Notify every 10 seconds

ui.run()
```

### Event Throttling and Debouncing

```python
# Throttled search-as-you-type
@ui.page('/')
def search_demo():
    results = ui.column()

    async def search(e):
        query = e.value
        if not query:
            results.clear()
            return

        # Simulate API call
        await asyncio.sleep(0.3)
        results.clear()
        with results:
            for i in range(5):
                ui.label(f'Result {i+1} for "{query}"')

    # Throttled input - limits API calls
    ui.input('Search', on_change=search).on(
        'update:model-value',
        search,
        throttle=0.5  # Wait 500ms between calls
    )

ui.run()
```

### Progressive Enhancement Patterns

```python
# Graceful degradation for complex interactions
@ui.page('/')
def enhanced_form():
    data = {'items': []}

    @ui.refreshable
    def item_list():
        if not data['items']:
            ui.label('No items yet').classes('text-grey')
            return

        for i, item in enumerate(data['items']):
            with ui.row().classes('items-center gap-2'):
                ui.label(item)
                ui.button(
                    icon='delete',
                    on_click=lambda i=i: remove_item(i)
                ).props('flat dense')

    def add_item():
        new_item = item_input.value.strip()
        if new_item:
            data['items'].append(new_item)
            item_input.value = ''
            item_list.refresh()
            ui.notify(f'Added: {new_item}', type='positive')

    def remove_item(index):
        removed = data['items'].pop(index)
        item_list.refresh()
        ui.notify(f'Removed: {removed}', type='negative')

    with ui.card().classes('w-full max-w-md mx-auto p-4'):
        ui.label('Dynamic Item List').classes('text-h6 mb-4')

        item_list()

        with ui.row().classes('w-full gap-2 mt-4'):
            item_input = ui.input('New item').classes('flex-grow')
            item_input.on('keydown.enter', add_item)
            ui.button('Add', on_click=add_item)

ui.run()
```

## Integration Points

### FastAPI Integration

```python
from fastapi import FastAPI
from nicegui import app, ui

# Access underlying FastAPI app
@app.get('/api/data')
async def get_data():
    return {'message': 'Hello from API'}

# Use in NiceGUI events
async def fetch_api_data():
    import httpx
    async with httpx.AsyncClient() as client:
        response = await client.get('http://localhost:8080/api/data')
        data = response.json()
        ui.notify(f"API Response: {data['message']}")

@ui.page('/')
def api_integration():
    ui.button('Fetch API Data', on_click=fetch_api_data)

ui.run()
```

### Database Integration

```python
import sqlite3
from contextlib import asynccontextmanager

# Database connection management
@asynccontextmanager
async def get_db():
    conn = sqlite3.connect('app.db')
    try:
        yield conn
    finally:
        conn.close()

async def save_user_data(user_data):
    async with get_db() as db:
        db.execute(
            'INSERT INTO users (name, email) VALUES (?, ?)',
            (user_data['name'], user_data['email'])
        )
        db.commit()

@ui.page('/')
def database_form():
    data = {'name': '', 'email': ''}

    ui.input('Name').bind_value(data, 'name')
    ui.input('Email').bind_value(data, 'email')

    async def submit():
        await save_user_data(data)
        ui.notify('User saved to database!', type='positive')

    ui.button('Save to DB', on_click=submit)

ui.run()
```

### External Service Integration

```python
# Integration with external APIs and services
class WeatherService:
    @staticmethod
    async def get_weather(city: str):
        # Simulate API call
        import random
        await asyncio.sleep(0.5)
        return {
            'city': city,
            'temperature': random.randint(15, 35),
            'condition': random.choice(['Sunny', 'Cloudy', 'Rainy'])
        }

@ui.page('/')
def weather_app():
    result_display = ui.column()

    async def fetch_weather():
        city = city_input.value.strip()
        if not city:
            ui.notify('Please enter a city name', type='warning')
            return

        ui.notify('Fetching weather...', type='ongoing')
        try:
            weather = await WeatherService.get_weather(city)
            result_display.clear()
            with result_display:
                ui.label(f"Weather in {weather['city']}").classes('text-h6')
                ui.label(f"Temperature: {weather['temperature']}°C")
                ui.label(f"Condition: {weather['condition']}")
        except Exception as e:
            ui.notify(f'Error: {e}', type='negative')

    city_input = ui.input('Enter city name')
    city_input.on('keydown.enter', fetch_weather)
    ui.button('Get Weather', on_click=fetch_weather)
    result_display

ui.run()
```

## Troubleshooting

### Common Event Issues

**Problem: Events not firing**

```python
# ❌ Wrong: Accessing value immediately after creation
input_field = ui.input('Name')
print(input_field.value)  # May be empty/undefined

# ✅ Correct: Use event handlers
def handle_change(e):
    print(f'Value changed to: {e.value}')

ui.input('Name', on_change=handle_change)
```

**Problem: Async event handlers not working**

```python
# ❌ Wrong: Missing await
def bad_handler():
    some_async_operation()  # Won't work properly

# ✅ Correct: Proper async handling
async def good_handler():
    await some_async_operation()
    ui.notify('Operation completed!')

ui.button('Process', on_click=good_handler)
```

**Problem: State not persisting**

```python
# ❌ Wrong: Using global variables
global_counter = 0  # Lost on server restart

# ✅ Correct: Use app.storage
def increment():
    current = app.storage.user.get('counter', 0)
    app.storage.user['counter'] = current + 1
    ui.notify(f'Counter: {app.storage.user["counter"]}')

ui.button('Increment', on_click=increment)
```

### Performance Issues

**Problem: Too many event handlers**

```python
# ❌ Wrong: Creating handlers in loops without cleanup
for i in range(1000):
    ui.button(f'Button {i}', on_click=lambda: handle_click(i))

# ✅ Correct: Use efficient patterns
def create_button_grid():
    def handle_click(button_id):
        ui.notify(f'Clicked button {button_id}')

    with ui.grid(columns=10):
        for i in range(100):  # Reasonable number
            ui.button(f'{i}', on_click=lambda i=i: handle_click(i))

create_button_grid()
```

**Problem: Memory leaks with timers**

```python
# ❌ Wrong: Not cleaning up timers
timer = ui.timer(1.0, some_function)
# Timer keeps running even when not needed

# ✅ Correct: Proper timer management
timer = ui.timer(1.0, some_function)

def cleanup():
    timer.cancel()
    ui.notify('Timer stopped')

ui.button('Stop Timer', on_click=cleanup)
```

### Binding Issues

**Problem: Binding not updating**

```python
# ❌ Wrong: Modifying non-bindable properties
class BadState:
    def __init__(self):
        self.value = 0

state = BadState()
ui.label().bind_text_from(state, 'value')  # Won't update automatically

# ✅ Correct: Use BindableProperty
from nicegui.binding import BindableProperty

class GoodState:
    value = BindableProperty()

    def __init__(self):
        self.value = 0

state = GoodState()
ui.label().bind_text_from(state, 'value')  # Updates automatically
```

**Problem: Circular binding dependencies**

```python
# ❌ Wrong: Creating circular references
obj1 = {'value': 0}
obj2 = {'value': 0}

ui.input().bind_value(obj1, 'value').bind_value(obj2, 'value')  # Circular

# ✅ Correct: Use one-way bindings or central state
central_state = {'value': 0}

ui.input().bind_value(central_state, 'value')
ui.label().bind_text_from(central_state, 'value')
```

### WebSocket Connection Issues

**Problem: Events lost during disconnection**

```python
# ❌ Wrong: Not handling connection state
def send_data():
    ui.notify('Data sent')  # May fail if disconnected

# ✅ Correct: Check connection state
async def send_data():
    if not ui.context.client.has_socket_connection:
        ui.notify('Connection lost, please refresh', type='warning')
        return

    ui.notify('Data sent successfully')

ui.button('Send', on_click=send_data)
```

## References

- **Official Documentation**: [NiceGUI Events](https://nicegui.io/documentation/events)
- **Binding Documentation**: [Property Binding](https://nicegui.io/documentation/section_binding_properties)
- **Storage Documentation**: [App Storage](https://nicegui.io/documentation/storage)
- **Timer Documentation**: [UI Timer](https://nicegui.io/documentation/timer)
- **Keyboard Documentation**: [Keyboard Events](https://nicegui.io/documentation/keyboard)
- **Notify Documentation**: [Notifications](https://nicegui.io/documentation/notify)

**Example Applications:**

- `examples/todo_list/` - Interactive task management with events
- `examples/chat_app/` - Real-time messaging with WebSocket
- `examples/search_as_you_type/` - Throttled API calls and async events
- `examples/editable_table/` - Custom events and data manipulation

**Related Guides:**

- Guide #02: NiceGUI Fundamentals (event handling basics)
- Guide #03: UI Components & Layout (component-specific events)
- Guide #05: Pages & Routing (navigation events)
- Guide #08: Data Storage & Persistence (state management)
- Guide #10: Advanced Integration (custom events, external systems)
