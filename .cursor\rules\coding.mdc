---
description: writing code
globs: *.py
alwaysApply: false
---

# Coding Guidelines for AI Assistants

**Primary Reference**: @CONTRIBUTING.md

## AI-Specific Notes

- Never overwrite .env files without explicit confirmation
- When making changes, always check for existing similar functionality in the codebase to avoid duplication
- Prefer editing existing files over creating new ones unless absolutely necessary
- For files over 200-300 lines, suggest refactoring opportunities
- When fixing bugs, exhaust existing implementation options before introducing new patterns
