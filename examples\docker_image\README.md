# Docker Example with NiceGUI

This README provides a walkthrough on how to utilize the NiceGUI release docker image, [zauberzeug/nicegui, available on Docker Hub](https://hub.docker.com/r/zauberzeug/nicegui).
The image is configured using a `docker-compose.yml` file for ease of use.
You can achieve similar results using the `docker run` command along with its appropriate parameters.

## Testing the Setup

Modify the `docker-compose.yml` file to reflect your local host user's uid/gid and then execute the command:

```bash
docker compose up
```

## Special Docker Features

### Data Persistence

NiceGUI automatically generates a `.nicegui` directory in the application's root directory (`/app` within the docker container).
In this example, the local `app` folder is mounted to the `/app` location inside the container, ensuring that the `.nicegui` folder remains persistent across docker restarts.
You can validate this by accessing http://localhost:8080, inputting some data for storage, and then restarting the container.

### Non-Root User Execution

The application within the container operates as a non-root user (similar to the [configs from linuxserver.io](https://docs.linuxserver.io/general/understanding-puid-and-pgid)).
Consequently, all files generated by NiceGUI (such as the `.nicegui` persistence) will bear the configured uid/gid.

### Docker Signal Pass-Through

The docker image is designed to relay signals from Docker, such as SIGTERM, to initiate a graceful shutdown of NiceGUI.
For instance, when you stop the container (using Ctrl+C) and subsequently examine the logs using the `docker compose logs` command,
you should notice the initiation of the `ui.shutdown` method.

### Storage Secret

In the example `main.py` we read the [storage secret](https://nicegui.io/documentation/storage) from a environment variable.
This can then be defined in the `docker-compose.yml` (or even passed on from an `.env` file).
