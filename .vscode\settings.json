{"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "prettier.printWidth": 120, "[json]": {"prettier.printWidth": 1}, "python.testing.pytestArgs": ["."], "python.testing.pytestEnabled": true, "python.testing.unittestEnabled": false, "files.insertFinalNewline": true, "[python]": {"editor.defaultFormatter": "ms-python.autopep8", "editor.codeActionsOnSave": {"source.fixAll.ruff": "always"}}}