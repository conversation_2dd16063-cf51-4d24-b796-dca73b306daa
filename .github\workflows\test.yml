name: Run Tests

on: [push]

concurrency:
  group: ${{ github.workflow }}-${{ github.sha }}
  cancel-in-progress: true

jobs:
  code-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5
      - name: set up Poetry
        uses: abatilo/actions-poetry@v4
      - name: set up Python
        uses: actions/setup-python@v6
        with:
          python-version: "3.8"
          cache: "poetry"
      - name: install dependencies
        run: poetry install --all-extras --with dev
      - name: pre-commit, mypy & pylint
        run: |
          poetry run pre-commit run --all-files
          poetry run mypy ./nicegui --non-interactive
          poetry run pylint ./nicegui
  test:
    strategy:
      matrix:
        python: ["3.8", "3.9", "3.10", "3.11", "3.12", "3.13"]
      fail-fast: false
    runs-on: ubuntu-latest
    timeout-minutes: 40
    steps:
      - uses: actions/checkout@v5
      - name: set up Poetry
        uses: abatilo/actions-poetry@v4
      - name: set up Python
        uses: actions/setup-python@v6
        with:
          python-version: ${{ matrix.python }}
          cache: "poetry"
      - name: install dependencies
        run: |
          set -x
          poetry install --all-extras
          # install packages to run the examples
          poetry run pip install opencv-python opencv-contrib-python-headless httpx isort replicate langchain openai simpy tortoise-orm
          poetry run pip install -r tests/requirements.txt
          # try fix issue with importlib_resources
          poetry run pip install importlib-resources
      - name: test startup
        run: poetry run ./test_startup.sh
      - name: setup chromedriver
        uses: nanasess/setup-chromedriver@v2.3.0
      - name: pytest
        run: poetry run pytest

  slack:
    needs:
      - test
      - code-check
    if: always() # also execute when test fail
    runs-on: ubuntu-latest
    steps:
      - name: Determine if we need to notify
        uses: Jimdo/should-i-notify-action@main
        id: should_notify
        with:
          needs_context: ${{ toJson(needs) }}
          github_token: ${{ secrets.GITHUB_TOKEN }}
      - name: Check if secret exists
        id: check_secret
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_ROBOTICS_CI_WEBHOOK }}
        run: |
          if [[ -z "$SLACK_WEBHOOK" ]]; then
            echo "slack_webhook_exists=false" >> $GITHUB_ENV
          else
            echo "slack_webhook_exists=true" >> $GITHUB_ENV
          fi
      - name: Slack workflow notification
        if: steps.should_notify.outputs.should_send_message == 'yes' && env.slack_webhook_exists == 'true'
        uses: Gamesight/slack-workflow-status@master
        with:
          repo_token: ${{ secrets.GITHUB_TOKEN }}
          slack_webhook_url: ${{ secrets.SLACK_ROBOTICS_CI_WEBHOOK }}
          channel: "robotik-ci"
          name: "NiceGUI"
