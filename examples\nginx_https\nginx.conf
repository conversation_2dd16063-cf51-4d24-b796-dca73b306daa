worker_processes  1;

events {
    worker_connections  1024;
}


http {
    include       mime.types;
    default_type  application/octet-stream;


    sendfile        on;
    keepalive_timeout  65;

    server {
        listen 80;
        listen [::]:80;
        server_name _;
        resolver 127.0.0.11; # Specific to running nginx proxy in docker
                             # See https://github.com/docker/compose/issues/3412

        # Redirect all HTTP requests to HTTPS
        return 301 https://$host$request_uri;
    }

    server {
        listen 443 ssl;
        listen [::]:443 ssl;
        http2 on;
        server_name _;
        resolver 127.0.0.11; # Specific to running nginx proxy in docker
                             # See https://github.com/docker/compose/issues/3412

        # SSL configuration
        ssl_certificate /certs/localhost.crt;
        ssl_certificate_key /certs/localhost.key;
        ssl_session_timeout 1d;

        # Proxy pass to app:8080
        location / {
            proxy_pass http://app:8080;
            proxy_redirect http://app:8080/ /;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "Upgrade";
        }
    }
}
